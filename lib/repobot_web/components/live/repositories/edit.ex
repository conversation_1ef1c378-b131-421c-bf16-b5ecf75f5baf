defmodule RepobotWeb.Live.Repositories.Edit do
  use RepobotWeb, :live_view

  alias Repobot.{Repositories, Folders}

  def mount(%{"id" => id}, _session, socket) do
    repository =
      Repositories.get_repository!(id) |> Repobot.Repo.preload([:folder, :template_folders])

    folders =
      Folders.list_folders(
        socket.assigns.current_user,
        nil,
        socket.assigns.current_organization.id
      )

    folder_settings =
      if repository.folder_id && !repository.template,
        do: Folders.get_folder!(repository.folder_id).settings,
        else: %{}

    {:ok,
     socket
     |> assign(:repository, repository)
     |> assign(:folders, folders)
     |> assign(:folder_settings, folder_settings)
     |> assign(:page_title, "Edit #{repository.full_name}")
     |> assign(:changeset, Repositories.change_repository(repository))}
  end

  def handle_event("save", %{"repository" => repository_params} = params, socket) do
    repository_params =
      case repository_params["settings"] do
        settings when is_binary(settings) ->
          case Jason.decode(settings) do
            {:ok, parsed_settings} ->
              Map.put(repository_params, "settings", parsed_settings)

            {:error, _} ->
              repository_params
          end

        _ ->
          repository_params
      end

    # Handle template folder selections if repository is a template
    template_folder_ids =
      (params["template_folders"] || %{})
      |> Map.keys()
      |> MapSet.new()

    # If becoming a template, ensure folder_id is cleared
    repository_params =
      if repository_params["template"] == "true" do
        Map.put(repository_params, "folder_id", nil)
      else
        repository_params
      end

    case Repositories.update_repository(socket.assigns.repository, repository_params) do
      {:ok, repository} ->
        # If repository is a template, update its template folders
        if repository.template do
          selected_folders =
            Enum.filter(socket.assigns.folders, &MapSet.member?(template_folder_ids, &1.id))

          # Update template folders
          repository
          |> Repobot.Repo.preload(:template_folders)
          |> Ecto.Changeset.change()
          |> Ecto.Changeset.put_assoc(:template_folders, selected_folders)
          |> Repobot.Repo.update()
        end

        {:noreply,
         socket
         |> put_flash(:info, "Repository updated successfully")
         |> push_navigate(to: ~p"/repositories/#{repository}")}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  defp folder_selected?(folder, repository) do
    (not repository.template && folder.id == repository.folder_id) ||
      Enum.any?(repository.template_folders, &(&1.id == folder.id))
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-2xl font-semibold text-slate-900">Edit {@repository.full_name}</h1>
        <.link navigate={~p"/repositories/#{@repository}"} class="link link-primary">
          Back to Repository
        </.link>
      </div>

      <div class="max-w-4xl">
        <.simple_form :let={f} for={@changeset} phx-submit="save">
          <div class="space-y-6">
            <!-- Template Settings Section -->
            <div class="card bg-base-100 shadow-sm border border-base-300">
              <div class="card-body">
                <h2 class="card-title text-lg mb-4">Template Settings</h2>
                <p class="text-sm text-base-content/70 mb-6">
                  Configure whether this repository serves as a template for other repositories.
                </p>

                <div class="form-control mb-6">
                  <label class="label cursor-pointer justify-start gap-3">
                    <.input type="checkbox" field={f[:template]} />
                    <div class="flex flex-col">
                      <span class="label-text font-medium">Template Repository</span>
                      <span class="label-text-alt text-base-content/60">
                        Enable this repository to serve as a template for creating new repositories
                      </span>
                    </div>
                  </label>
                </div>

                <div class="alert alert-success mb-4">
                  <.icon name="hero-information-circle" class="size-5 shrink-0" />
                  <div class="text-sm">
                    <p class="font-medium mb-2">When Template is enabled:</p>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                      <li>
                        Source files added to this repository are automatically propagated to all target repositories in folders
                      </li>
                      <li>
                        Template variables like
                        <code class="bg-base-200 px-1 rounded">{"{{ project }}"}</code>
                        and <code class="bg-base-200 px-1 rounded">{"{{ owner }}"}</code>
                        can be used to customize files
                      </li>
                      <li>
                        The repository will be removed from its primary folder assignment (templates can belong to multiple folders)
                      </li>
                      <li>
                        Changes to source files will be synchronized to target repositories based on the sync mode setting
                      </li>
                    </ul>
                  </div>
                </div>

                <%= if @repository.template do %>
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-medium">Template Folders</span>
                    </label>
                    <div class="space-y-2">
                      <%= for folder <- @folders do %>
                        <div class="form-control">
                          <label class="label cursor-pointer justify-start gap-3">
                            <input
                              type="checkbox"
                              name={"template_folders[#{folder.id}]"}
                              value="true"
                              checked={folder_selected?(folder, @repository)}
                              class="checkbox checkbox-primary"
                            />
                            <span class="label-text">{folder.name}</span>
                          </label>
                        </div>
                      <% end %>
                      <div class="label">
                        <span class="label-text-alt text-base-content/60">
                          Select the folders this template repository should be associated with.
                        </span>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
            
    <!-- Sync Mode Settings Section -->
            <div class="card bg-base-100 shadow-sm border border-base-300">
              <div class="card-body">
                <h2 class="card-title text-lg mb-4">Synchronization Mode</h2>
                <p class="text-sm text-base-content/70 mb-6">
                  Choose how changes from this template repository are applied to target repositories.
                </p>

                <div class="form-control mb-4">
                  <label class="label">
                    <span class="label-text font-medium">Sync Mode</span>
                  </label>
                  <div class="space-y-3">
                    <div class="form-control">
                      <label class="label cursor-pointer justify-start gap-3">
                        <input
                          type="radio"
                          name="repository[sync_mode]"
                          value="pr"
                          class="radio radio-primary"
                          checked={
                            Phoenix.HTML.Form.normalize_value("radio", @changeset.data.sync_mode) ==
                              "pr"
                          }
                        />
                        <div class="flex flex-col">
                          <span class="label-text font-medium">Pull Requests (Recommended)</span>
                          <span class="label-text-alt text-base-content/60">
                            Changes are proposed via pull requests, allowing review before merging
                          </span>
                        </div>
                      </label>
                    </div>
                    <div class="form-control">
                      <label class="label cursor-pointer justify-start gap-3">
                        <input
                          type="radio"
                          name="repository[sync_mode]"
                          value="direct"
                          class="radio radio-primary"
                          checked={
                            Phoenix.HTML.Form.normalize_value("radio", @changeset.data.sync_mode) ==
                              "direct"
                          }
                        />
                        <div class="flex flex-col">
                          <span class="label-text font-medium">Direct Updates</span>
                          <span class="label-text-alt text-base-content/60">
                            Changes are applied immediately to target repositories without review
                          </span>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>

                <div class="alert alert-warning">
                  <.icon name="hero-exclamation-triangle" class="size-5 shrink-0" />
                  <div class="text-sm">
                    <p class="font-medium mb-1">Sync Mode Impact:</p>
                    <p>
                      This setting only applies when this repository is used as a template. It determines how source file changes are synchronized to target repositories in folders.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
    <!-- Folder Settings Section (if applicable) -->
            <%= if @repository.folder_id && !@repository.template do %>
              <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body">
                  <h2 class="card-title text-lg mb-4">Folder Settings (Inherited)</h2>
                  <p class="text-sm text-base-content/70 mb-6">
                    These settings are inherited from the folder and can be overridden in the repository settings below.
                  </p>

                  <div class="form-control">
                    <pre class="bg-base-200 p-4 rounded-lg text-sm font-mono overflow-x-auto"><%= Jason.encode_to_iodata!(@folder_settings, pretty: true) %></pre>
                  </div>
                </div>
              </div>
            <% end %>
            
    <!-- Repository Settings Section -->
            <div class="card bg-base-100 shadow-sm border border-base-300">
              <div class="card-body">
                <h2 class="card-title text-lg mb-4">Repository Settings</h2>
                <p class="text-sm text-base-content/70 mb-6">
                  Configure JSON settings that override folder-level settings for this repository.
                </p>

                <textarea
                  name="repository[settings]"
                  class="textarea textarea-bordered w-full font-mono text-sm h-32"
                  placeholder='{"key": "value"}'
                ><%= Jason.encode_to_iodata!(@repository.settings, pretty: true) %></textarea>
              </div>
            </div>
            
    <!-- Form Actions -->
            <div class="card bg-base-100 shadow-sm border border-base-300">
              <div class="card-body">
                <div class="flex items-center justify-end gap-4">
                  <.link navigate={~p"/repositories/#{@repository}"} class="btn btn-ghost">
                    Cancel
                  </.link>
                  <.button variant="primary">Save Changes</.button>
                </div>
              </div>
            </div>
          </div>
        </.simple_form>
      </div>
    </div>
    """
  end
end
