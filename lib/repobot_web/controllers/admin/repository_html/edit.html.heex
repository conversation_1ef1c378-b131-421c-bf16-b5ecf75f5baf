<div class="p-6">
  <nav class="mb-8">
    <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
      <li>
        <.link navigate={~p"/admin/repositories"} class="link link-primary">
          Repositories
        </.link>
      </li>
      <li>•</li>
      <li>
        <.link navigate={~p"/admin/repositories/#{@repository}"} class="link link-primary">
          {@repository.name}
        </.link>
      </li>
      <li>•</li>
      <li class="font-medium text-slate-900">Edit</li>
    </ol>
  </nav>

  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-slate-900">Edit Repository</h1>
    <p class="mt-2 text-sm text-slate-600">Update repository information and settings.</p>
  </div>

  <div class="max-w-4xl">
    <.form :let={f} for={@changeset} action={~p"/admin/repositories/#{@repository}"} method="put">
      <div class="space-y-6">
        <!-- Basic Information Section -->
        <div class="card bg-base-100 shadow-sm border border-base-300">
          <div class="card-body">
            <h2 class="card-title text-lg mb-4">Basic Information</h2>
            <p class="text-sm text-base-content/70 mb-6">
              Configure the basic repository details and visibility settings.
            </p>
            <div class="space-y-4">
              <div class="form-control">
                <.input field={f[:name]} type="text" label="Name" />
              </div>
              <div class="form-control">
                <.input field={f[:full_name]} type="text" label="Full Name" />
              </div>
              <div class="form-control">
                <.input field={f[:owner]} type="text" label="Owner" />
              </div>
              <div class="form-control">
                <.input field={f[:language]} type="text" label="Language" />
              </div>
              <div class="flex flex-wrap gap-6">
                <div class="form-control">
                  <label class="label cursor-pointer gap-3">
                    <.input field={f[:private]} type="checkbox" />
                    <span class="label-text font-medium">Private Repository</span>
                  </label>
                </div>
                <div class="form-control">
                  <label class="label cursor-pointer gap-3">
                    <.input field={f[:fork]} type="checkbox" />
                    <span class="label-text font-medium">Fork</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        
<!-- Template Settings Section -->
        <div class="card bg-base-100 shadow-sm border border-base-300">
          <div class="card-body">
            <h2 class="card-title text-lg mb-4">Template Settings</h2>
            <p class="text-sm text-base-content/70 mb-6">
              Configure whether this repository serves as a template for other repositories.
            </p>

            <div class="form-control mb-6">
              <label class="label cursor-pointer justify-start gap-3">
                <.input field={f[:template]} type="checkbox" />
                <div class="flex flex-col">
                  <span class="label-text font-medium">Template Repository</span>
                  <span class="label-text-alt text-base-content/60">
                    Enable this repository to serve as a template for creating new repositories
                  </span>
                </div>
              </label>
            </div>

            <div class="alert alert-info mb-4">
              <.icon name="hero-information-circle" class="size-5 shrink-0" />
              <div class="text-sm">
                <p class="font-medium mb-2">When Template is enabled:</p>
                <ul class="list-disc list-inside space-y-1 text-sm">
                  <li>
                    Source files added to this repository are automatically propagated to all target repositories in folders
                  </li>
                  <li>
                    Template variables like
                    <code class="bg-base-200 px-1 rounded">{"{{ project }}"}</code>
                    and <code class="bg-base-200 px-1 rounded">{"{{ owner }}"}</code>
                    can be used to customize files
                  </li>
                  <li>
                    The repository will be removed from its primary folder assignment (templates can belong to multiple folders)
                  </li>
                  <li>
                    Changes to source files will be synchronized to target repositories based on the sync mode setting
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
<!-- Sync Mode Settings Section -->
        <div class="card bg-base-100 shadow-sm border border-base-300">
          <div class="card-body">
            <h2 class="card-title text-lg mb-4">Synchronization Mode</h2>
            <p class="text-sm text-base-content/70 mb-6">
              Choose how changes from this template repository are applied to target repositories.
            </p>

            <div class="form-control mb-4">
              <label class="label">
                <span class="label-text font-medium">Sync Mode</span>
              </label>
              <div class="space-y-3">
                <div class="form-control">
                  <label class="label cursor-pointer justify-start gap-3">
                    <input
                      type="radio"
                      name="repository[sync_mode]"
                      value="pr"
                      class="radio radio-primary"
                      checked={
                        Phoenix.HTML.Form.normalize_value("radio", @changeset.data.sync_mode) ==
                          "pr"
                      }
                    />
                    <div class="flex flex-col">
                      <span class="label-text font-medium">Pull Requests (Recommended)</span>
                      <span class="label-text-alt text-base-content/60">
                        Changes are proposed via pull requests, allowing review before merging
                      </span>
                    </div>
                  </label>
                </div>
                <div class="form-control">
                  <label class="label cursor-pointer justify-start gap-3">
                    <input
                      type="radio"
                      name="repository[sync_mode]"
                      value="direct"
                      class="radio radio-primary"
                      checked={
                        Phoenix.HTML.Form.normalize_value("radio", @changeset.data.sync_mode) ==
                          "direct"
                      }
                    />
                    <div class="flex flex-col">
                      <span class="label-text font-medium">Direct Updates</span>
                      <span class="label-text-alt text-base-content/60">
                        Changes are applied immediately to target repositories without review
                      </span>
                    </div>
                  </label>
                </div>
              </div>
            </div>

            <div class="alert alert-warning">
              <.icon name="hero-exclamation-triangle" class="size-5 shrink-0" />
              <div class="text-sm">
                <p class="font-medium mb-1">Sync Mode Impact:</p>
                <p>
                  This setting only applies when this repository is used as a template. It determines how source file changes are synchronized to target repositories in folders.
                </p>
              </div>
            </div>
          </div>
        </div>
        
<!-- Repository Settings Section -->
        <div class="card bg-base-100 shadow-sm border border-base-300">
          <div class="card-body">
            <h2 class="card-title text-lg mb-4">Repository Settings</h2>
            <p class="text-sm text-base-content/70 mb-6">
              Configure JSON settings that override folder-level settings for this repository.
            </p>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Settings (JSON)</span>
              </label>
              <textarea
                name="repository[settings]"
                class="textarea textarea-bordered font-mono text-sm h-32"
                placeholder='{"key": "value"}'
              ><%= Jason.encode_to_iodata!(@repository.settings, pretty: true) %></textarea>
              <div class="label">
                <span class="label-text-alt text-base-content/60">
                  Enter JSON settings for this repository. These settings will override any folder settings.
                </span>
              </div>
            </div>
          </div>
        </div>
        
<!-- Form Actions -->
        <div class="card bg-base-100 shadow-sm border border-base-300">
          <div class="card-body">
            <div class="flex items-center justify-end gap-4">
              <.link navigate={~p"/admin/repositories/#{@repository}"} class="btn btn-ghost">
                Cancel
              </.link>
              <button type="submit" class="btn btn-primary">
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    </.form>
  </div>
</div>
