defmodule Repobot.MixProject do
  use Mix.Project

  def project do
    [
      app: :repobot,
      version: "0.1.0",
      elixir: "~> 1.14",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      test_coverage: [tool: ExCoveralls],
      deps: deps(),
      listeners: [Phoenix.CodeReloader]
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {Repobot.Application, []},
      extra_applications: [:logger, :runtime_tools]
    ]
  end

  def cli do
    [
      preferred_envs: [
        "coveralls.html": :test,
        "coveralls.json": :test
      ]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    [
      {:igniter, "~> 0.6", only: [:dev, :test]},
      {:excoveralls, "~> 0.18", only: [:dev, :test]},
      {:phoenix, "~> 1.8.0-rc.3"},
      {:phoenix_ecto, "~> 4.4"},
      {:phoenix_html, "~> 4.0"},
      {:phoenix_live_reload, "~> 1.2", only: :dev},
      {:phoenix_live_view, "~> 1.0"},
      {:ecto_sql, "~> 3.10"},
      {:postgrex, ">= 0.0.0"},
      {:ueberauth, "~> 0.10"},
      {:ueberauth_github, "~> 0.8.3"},
      {:floki, ">= 0.30.0", only: :test},
      {:phoenix_live_dashboard, "~> 0.8.3"},
      {:esbuild, "~> 0.9", runtime: Mix.env() == :dev},
      {:tailwind, "~> 0.3", runtime: Mix.env() == :dev},
      {:req, "~> 0.5"},
      {:heroicons,
       github: "tailwindlabs/heroicons",
       tag: "v2.1.1",
       sparse: "optimized",
       app: false,
       compile: false,
       depth: 1},
      {:swoosh, "~> 1.5"},
      {:finch, "~> 0.13"},
      {:telemetry_metrics, "~> 1.0"},
      {:telemetry_poller, "~> 1.0"},
      {:gettext, "~> 0.20"},
      {:jason, "~> 1.2"},
      {:dns_cluster, "~> 0.2"},
      {:bandit, "~> 1.2"},
      {:tentacat, "~> 2.0"},
      {:drops, "~> 0.2", github: "solnic/drops", branch: "operations"},
      {:solid, "~> 1.0"},
      {:jose, "~> 1.11"},
      {:text_diff, "~> 0.1.0"},
      {:mox, "~> 1.1", only: :test},
      {:sentry, "~> 11.0"},
      {:opentelemetry, "~> 1.5"},
      {:opentelemetry_api, "~> 1.4"},
      {:opentelemetry_exporter, "~> 1.0"},
      {:opentelemetry_semantic_conventions, "~> 1.27"},
      {:opentelemetry_phoenix, "~> 2.0"},
      {:opentelemetry_ecto, "~> 1.2"},
      {:cloak, "~> 1.1"},
      {:cloak_ecto, "~> 1.3.0"},
      {:basic_auth, "~> 2.2.2"},
      {:scrivener_ecto, "~> 2.7"},
      {:logger_file_backend, "~> 0.0.13"},
      {:logger_json, "~> 7.0"},
      {:oban, "~> 2.19"},
      {:oban_web, "~> 2.11"},
      {:oban_notifiers_phoenix, "~> 0.2.0"},
      {:live_debugger, "~> 0.2", only: :dev},
      {:tidewave, "~> 0.1", only: :dev}
    ]
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ecto.setup", "assets.setup", "assets.build"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.setup": ["tailwind.install --if-missing", "esbuild.install --if-missing"],
      "assets.build": ["tailwind repobot", "esbuild repobot"],
      "assets.deploy": [
        "tailwind repobot --minify",
        "esbuild repobot --minify",
        "phx.digest"
      ]
    ]
  end
end
